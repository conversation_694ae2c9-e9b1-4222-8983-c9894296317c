import { EntityID } from './common';

export enum SegmentEvents {
  PRODUCT_VIEWED = 'Product Viewed',
  PRODUCT_SEARCHED = 'Products Searched',
  PRODUCT_LIST_VIEWED = 'Product List Viewed',
  LISTING_DETAIL_VIEWED = 'Listing Detail Viewed',
  BOOKING_REQUESTED = 'Booking Requested',
  CONTACT_AGENT = 'Contact Agent',
  SELL_FORM_SUBMITTED = 'Sell Form Submitted',
  EMAIL_SUBSCRIBED = 'Email Subscribed',
  BOOKING_REQUEST_CLICKED = 'Booking Request Clicked',
}

export interface AnalyticsProductItem {
  name: string;
  product_id: EntityID;
  price: EntityID;
  brand: string;
  category: string;
  index: number;
  image_url: any;
  label?: any;
  object?: any;
  url: string;
  value?: any;
}
