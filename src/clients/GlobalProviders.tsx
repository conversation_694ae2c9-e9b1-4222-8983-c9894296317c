'use client';

import { ReactNode, useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';
import { usePathname, useSearchParams } from 'next/navigation';

dayjs.extend(localeData);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(isBetween);
dayjs().localeData();

type Props = {
  header: ReactNode;
  footer: ReactNode;
  children: ReactNode;
};

const GlobalProviders = ({ children, header, footer }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  // const [hideHeader, setHideHeader] = useState(false);
  const hideHeader = useMemo(() => pathname.includes('lead-request'), [pathname]);

  useEffect(() => {
    window.analytics.page({
      path: pathname,
      url: document.URL,
      search: searchParams.toString(),
      referrer: document.referrer,
    });
  }, [pathname, searchParams]);

  useEffect(() => {
    if ((document?.getElementById('cnc_mobile_menu_drawer') as any)?.checked) {
      document?.getElementById('cnc_mobile_menu_drawer')?.click();
    }
  }, [pathname]);

  // useEffect(() => {
  //   // Safe to run only on client
  //   if (pathname.includes('lead-request')) {
  //     setHideHeader(true);
  //   } else {
  //     setHideHeader(false);
  //   }
  // }, [pathname, searchParams]);

  console.log('pathname', pathname);

  return (
    <>
      {!hideHeader && header}
      {children}
      {!hideHeader && footer}
      <ProgressBar color='#d9eaf0' options={{ showSpinner: false }} shallowRouting />
    </>
  );
};

export default GlobalProviders;

declare global {
  interface Window {
    analytics: {
      page: any;
      track: any;
      reset: any;
    };
  }
}
