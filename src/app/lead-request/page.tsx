import CountdownTimer from '@/clients/components/lead-request/CountdownTimer';

export default function LeadRequest() {
  return (
    <div className='-mt-[80px] md:-mt-[90px]'>
      <div className='bg-[#E5F1F4] p-4 w-full text-center'>
        <p className='text-2xl font-bold'>New Lead</p>
        <p className='text-sm'>Exclusively yours for</p>
        <div className='flex items-center justify-center gap-x-2'>
          <svg
            width='34'
            height='34'
            viewBox='0 0 34 34'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M16.9999 1.92664C16.374 1.92664 15.8666 2.43404 15.8666 3.05998V7.98399C15.8666 8.60991 16.374 9.11733 16.9999 9.11733C17.6259 9.11733 18.1333 8.60991 18.1333 7.98399V4.24277C24.6754 4.81642 29.8067 10.309 29.8067 17C29.8067 24.0729 24.0729 29.8067 16.9999 29.8067C9.92702 29.8067 4.19328 24.0729 4.19328 17C4.19328 13.8406 5.33588 10.9509 7.23177 8.7174C7.63682 8.24022 7.57834 7.52502 7.10114 7.11996C6.62396 6.71491 5.90876 6.77339 5.50371 7.25057C3.27317 9.87834 1.92661 13.2833 1.92661 17C1.92661 25.3248 8.67516 32.0734 16.9999 32.0734C25.3248 32.0734 32.0733 25.3248 32.0733 17C32.0733 8.67519 25.3248 1.92664 16.9999 1.92664ZM15.2784 18.3163L9.57355 10.3641C9.41189 10.1387 9.43717 9.82947 9.63328 9.63336C9.82937 9.43724 10.1386 9.41197 10.364 9.57363L18.3162 15.2785C19.4078 16.0616 19.5365 17.6368 18.5866 18.5867C17.6367 19.5366 16.0615 19.4079 15.2784 18.3163Z'
              fill='black'
            />
          </svg>
          <CountdownTimer initialSeconds={900} />
        </div>
      </div>
      <div className='p-5 w-full md:w-[380px] mx-auto'>
        <p className='text-[#43555A] text-sm font-semibold'>Listing Address</p>
        <div className='flex items-center gap-x-2 mt-2'>
          <svg
            width='14'
            height='14'
            viewBox='0 0 14 14'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M7 7.83417C8.00516 7.83417 8.82 7.01933 8.82 6.01417C8.82 5.00901 8.00516 4.19417 7 4.19417C5.99484 4.19417 5.18 5.00901 5.18 6.01417C5.18 7.01933 5.99484 7.83417 7 7.83417Z'
              stroke='#4C737F'
              strokeWidth='1.5'
            />
            <path
              d='M2.11167 4.95251C3.26083 -0.0991602 10.745 -0.0933267 11.8883 4.95834C12.5592 7.92167 10.7158 10.43 9.1 11.9817C7.9275 13.1133 6.0725 13.1133 4.89417 11.9817C3.28417 10.43 1.44083 7.91584 2.11167 4.95251Z'
              stroke='#4C737F'
              strokeWidth='1.5'
            />
          </svg>
          <p className='text-sm font-medium text-[#6E7E83]'>123 Main Street</p>
        </div>
        <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
        <p className='text-[#43555A] text-sm font-semibold'>Arrival Date</p>
        <div className='flex items-center gap-x-2 mt-2'>
          <svg
            width='14'
            height='14'
            viewBox='0 0 14 14'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M4.66667 1.16663V2.91663'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M9.33333 1.16663V2.91663'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M2.04167 5.30249H11.9583'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M12.25 4.95829V9.91663C12.25 11.6666 11.375 12.8333 9.33333 12.8333H4.66667C2.625 12.8333 1.75 11.6666 1.75 9.91663V4.95829C1.75 3.20829 2.625 2.04163 4.66667 2.04163H9.33333C11.375 2.04163 12.25 3.20829 12.25 4.95829Z'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M6.99737 7.99162H7.00261'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M4.83835 7.99162H4.84359'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M4.83835 9.74162H4.84359'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
          </svg>
          <p className='text-sm font-medium text-[#6E7E83]'>Saturday, Oct 14, 2025</p>
        </div>
        <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
        <p className='text-[#43555A] text-sm font-semibold'>Departure Date</p>
        <div className='flex items-center gap-x-2 mt-2'>
          <svg
            width='14'
            height='14'
            viewBox='0 0 14 14'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M4.66667 1.16663V2.91663'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M9.33333 1.16663V2.91663'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M2.04167 5.30249H11.9583'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M12.25 4.95829V9.91663C12.25 11.6666 11.375 12.8333 9.33333 12.8333H4.66667C2.625 12.8333 1.75 11.6666 1.75 9.91663V4.95829C1.75 3.20829 2.625 2.04163 4.66667 2.04163H9.33333C11.375 2.04163 12.25 3.20829 12.25 4.95829Z'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeMiterlimit='10'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M6.99737 7.99162H7.00261'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M4.83835 7.99162H4.84359'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M4.83835 9.74162H4.84359'
              stroke='#4C737F'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
          </svg>
          <p className='text-sm font-medium text-[#6E7E83]'>Saturday, Oct 14, 2025</p>
        </div>
        <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
        <p className='text-[#43555A] text-sm font-semibold'>Guests</p>
        <div className='flex items-center gap-x-2 mt-2'>
          <svg
            width='14'
            height='14'
            viewBox='0 0 14 14'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M7 6.99996C8.61083 6.99996 9.91667 5.69412 9.91667 4.08329C9.91667 2.47246 8.61083 1.16663 7 1.16663C5.38917 1.16663 4.08333 2.47246 4.08333 4.08329C4.08333 5.69412 5.38917 6.99996 7 6.99996Z'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M12.0108 12.8333C12.0108 10.5758 9.765 8.75 7 8.75C4.235 8.75 1.98917 10.5758 1.98917 12.8333'
              stroke='#4C737F'
              strokeWidth='1.5'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
          </svg>
          <p className='text-sm font-medium text-[#6E7E83]'>3 adults, 0 children</p>
        </div>
        <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
        <p className='text-[#43555A] text-sm font-semibold'>Notes</p>
        <p className='text-sm font-medium text-[#6E7E83] mt-2 pl-6'>
          Stayed there several times. Please reply with rental rate. Thanks!
        </p>
        <div className='flex items-center justify-center gap-x-8 mt-8'>
          <div className='cursor-pointer'>
            <svg
              width='100'
              height='100'
              viewBox='0 0 100 100'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                opacity='0.4'
                d='M50 91.6667C73.0119 91.6667 91.6667 73.0119 91.6667 50C91.6667 26.9882 73.0119 8.33337 50 8.33337C26.9881 8.33337 8.33334 26.9882 8.33334 50C8.33334 73.0119 26.9881 91.6667 50 91.6667Z'
                fill='#FF0000'
              />
              <path
                d='M54.4167 50L64 40.4167C65.2083 39.2083 65.2083 37.2083 64 36C62.7917 34.7917 60.7917 34.7917 59.5833 36L50 45.5833L40.4167 36C39.2083 34.7917 37.2083 34.7917 36 36C34.7917 37.2083 34.7917 39.2083 36 40.4167L45.5833 50L36 59.5833C34.7917 60.7917 34.7917 62.7917 36 64C36.625 64.625 37.4167 64.9167 38.2083 64.9167C39 64.9167 39.7917 64.625 40.4167 64L50 54.4167L59.5833 64C60.2083 64.625 61 64.9167 61.7917 64.9167C62.5833 64.9167 63.375 64.625 64 64C65.2083 62.7917 65.2083 60.7917 64 59.5833L54.4167 50Z'
                fill='#FF0000'
              />
            </svg>
          </div>
          <div className='cursor-pointer'>
            <svg
              width='100'
              height='100'
              viewBox='0 0 100 100'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                opacity='0.4'
                d='M50 91.6667C73.0119 91.6667 91.6667 73.0119 91.6667 50C91.6667 26.9882 73.0119 8.33337 50 8.33337C26.9881 8.33337 8.33334 26.9882 8.33334 50C8.33334 73.0119 26.9881 91.6667 50 91.6667Z'
                fill='#56AD2E'
              />
              <path
                d='M44.0834 64.9166C43.25 64.9166 42.4584 64.5833 41.875 63.9999L30.0834 52.2083C28.875 50.9999 28.875 48.9999 30.0834 47.7916C31.2917 46.5833 33.2917 46.5833 34.5 47.7916L44.0834 57.3749L65.5 35.9583C66.7084 34.7499 68.7084 34.7499 69.9167 35.9583C71.125 37.1666 71.125 39.1666 69.9167 40.3749L46.2917 63.9999C45.7084 64.5833 44.9167 64.9166 44.0834 64.9166Z'
                fill='#56AD2E'
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
