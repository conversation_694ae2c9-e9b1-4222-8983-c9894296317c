import { Suspense } from 'react';

import Drawer from '@/app/ui/drawer';
import Footer from '@/app/ui/footer';
import Header from '@/app/ui/header';
import GlobalProviders from '@/clients/GlobalProviders';
import * as snippet from '@segment/snippet';

import type { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import Script from 'next/script';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';

import './globals.css';

const poppins = Poppins({
  weight: ['200', '300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON> and <PERSON>',
  description: 'Nantucket Real Estate and Rentals | Since 1931',
};

function renderSnippet() {
  const opts = {
    apiKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
    // note: the page option only covers SSR tracking.
    page: true,
  };

  if (process.env.NODE_ENV === 'development') {
    return snippet.max(opts);
  }

  return snippet.min(opts);
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en'>
      <Script
        strategy='afterInteractive'
        id='segment-script'
        dangerouslySetInnerHTML={{ __html: renderSnippet() }}
      />
      <body className={`${poppins.className} relative text-default-font antialiased bg-white`}>
        <Drawer>
          <Suspense>
            <GlobalProviders header={<Header />} footer={<Footer />}>
              <>
                <div className='min-h-[85vh] overflow-x-hidden pt-[80px] md:pt-[90px]'>
                  {children}
                </div>
              </>
            </GlobalProviders>
          </Suspense>
        </Drawer>
      </body>
    </html>
  );
}
